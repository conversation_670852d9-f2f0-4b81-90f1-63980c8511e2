"""
Request models for the onboarding API.

This module defines Pydantic models for validating incoming requests.
"""

from typing import Optional
from pydantic import BaseModel, EmailStr, Field, validator


class MessageRequest(BaseModel):
    """Request model for message processing."""
    
    message: str = Field(
        ...,
        min_length=1,
        max_length=1000,
        description="The message to process"
    )
    
    @validator('message')
    def validate_message(cls, v):
        """Validate and clean the message."""
        if not v or not v.strip():
            raise ValueError('Message cannot be empty')
        return v.strip()


class LoginRequest(BaseModel):
    """Request model for user login."""
    
    email: EmailStr = Field(
        ...,
        description="User's email address"
    )
    password: str = Field(
        ...,
        min_length=1,
        description="User's password"
    )
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password is not empty."""
        if not v or not v.strip():
            raise ValueError('Password cannot be empty')
        return v


class SignupRequest(BaseModel):
    """Request model for user signup."""
    
    name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="User's full name"
    )
    email: EmailStr = Field(
        ...,
        description="User's email address"
    )
    password: str = Field(
        ...,
        min_length=6,
        description="User's password (minimum 6 characters)"
    )
    city: Optional[str] = Field(
        None,
        max_length=100,
        description="User's city (optional)"
    )
    
    @validator('name')
    def validate_name(cls, v):
        """Validate and clean the name."""
        if not v or not v.strip():
            raise ValueError('Name cannot be empty')
        return v.strip()
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password requirements."""
        if not v or len(v) < 6:
            raise ValueError('Password must be at least 6 characters long')
        return v
    
    @validator('city')
    def validate_city(cls, v):
        """Validate and clean the city name."""
        if v is not None:
            v = v.strip()
            if not v:
                return None
        return v
