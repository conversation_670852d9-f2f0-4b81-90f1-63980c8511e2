"""
Session related schemas.
"""
from datetime import datetime
from typing import Any, Dict, List, Optional
from pydantic import BaseModel

class SessionBase(BaseModel):
    user_id: int
    metadata: Optional[Dict[str, Any]] = {}
    is_active: bool = True

class SessionCreate(SessionBase):
    pass

class SessionUpdate(SessionBase):
    metadata: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class SessionInDBBase(SessionBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class Session(SessionInDBBase):
    pass

class ChatResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
