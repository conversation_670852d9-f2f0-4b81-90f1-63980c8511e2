"""
Comprehensive validation tests for the onboarding API.

This module combines all validation-related tests including:
- City name validation
- Email validation
- Real-time field validation
"""

import pytest
import requests
import json
import uuid
from unittest.mock import Mock, patch
from app.models.requests import SignupRequest
from app.services.city_service import CityService
from app.services.session_service import SessionService
from app.models.session import FlowType, FlowStep
from app.core.exceptions import SessionError, ValidationError

# Test data
TEST_SESSION_ID = f"test-session-{uuid.uuid4()}"
BASE_URL = "http://localhost:8000"

class TestCityNameValidation:
    """Tests for city name validation functionality."""

    def test_city_name_validation_success(self):
        """Test successful city name validation."""
        with patch('app.services.city_service.CityService.find_city_by_name') as mock_find:
            mock_find.return_value = {
                'id': 'city_123',
                'name': 'New York',
                'code': 'NYC'
            }
            
            city_service = CityService()
            city_id = city_service.validate_city_name_and_get_id("New York")
            assert city_id == 'city_123'

    def test_city_name_validation_not_found(self):
        """Test city name validation when city is not found."""
        with patch('app.services.city_service.CityService.find_city_by_name') as mock_find:
            mock_find.return_value = None
            
            city_service = CityService()
            result = city_service.validate_city_name_and_get_id("Nonexistent City")
            assert result is None
            mock_find.assert_called_once_with("Nonexistent City")


class TestEmailValidation:
    """Tests for email validation functionality."""

    def test_invalid_email_format(self):
        """Test that invalid email format is rejected."""
        from pydantic import BaseModel, EmailStr, ValidationError
        
        class EmailModel(BaseModel):
            email: EmailStr
        
        # Test with invalid email
        with pytest.raises(ValidationError) as exc_info:
            EmailModel(email="invalid-email")
        
        # Verify the error message - Pydantic v2 includes more detailed error info
        error_str = str(exc_info.value)
        assert any(msg in error_str for msg in [
            "value is not a valid email address",
            "Input should be a valid email address"
        ]), f"Unexpected error message: {error_str}"

    def test_valid_email_format(self):
        """Test that valid email format is accepted."""
        from pydantic import BaseModel, EmailStr
        
        class EmailModel(BaseModel):
            email: EmailStr
        
        # Test with valid email
        email_model = EmailModel(email="<EMAIL>")
        assert email_model.email == "<EMAIL>"


class TestRealtimeValidation:
    """Tests for real-time form field validation."""

    def test_realtime_email_validation(self):
        """Test real-time email validation during form filling."""
        from pydantic import BaseModel, EmailStr, ValidationError
        
        class EmailModel(BaseModel):
            email: EmailStr
        
        # Test invalid email
        with pytest.raises(ValidationError):
            EmailModel(email="invalid-email")
        
        # Test valid email
        email_model = EmailModel(email="<EMAIL>")
        assert email_model.email == "<EMAIL>"

    def test_realtime_city_validation(self, monkeypatch):
        """Test real-time city validation during form filling."""
        # Create a mock for find_city_by_name
        mock_city_data = {
            'id': 'city_123',
            'name': 'New York',
            'code': 'NYC'
        }
        
        # Create a mock class to replace the method
        original_find = CityService.find_city_by_name
        
        def mock_find_city_by_name(self, city_name):
            if city_name == "New York":
                return mock_city_data
            return None
        
        # Apply the monkeypatch to replace find_city_by_name
        monkeypatch.setattr('app.services.city_service.CityService.find_city_by_name', mock_find_city_by_name)
        
        try:
            # Test with a valid city name
            city_service = CityService()
            city_id = city_service.validate_city_name_and_get_id("New York")
            
            # Verify the city ID is returned correctly
            assert city_id == 'city_123', f"Expected city_id 'city_123', got {city_id}"
            
            # Test with an invalid city name
            city_id = city_service.validate_city_name_and_get_id("Nonexistent City")
            assert city_id is None, "Expected None for non-existent city"
            
        finally:
            # Restore the original method
            monkeypatch.setattr('app.services.city_service.CityService.find_city_by_name', original_find)


# Fixtures for the tests
@pytest.fixture
def client():
    """Test client fixture."""
    from fastapi.testclient import TestClient
    from app.main import app
    return TestClient(app)

@pytest.fixture
def mock_city_service(monkeypatch):
    """Mock city service fixture."""
    # Create a mock implementation of CityService
    class MockCityService:
        def __init__(self):
            self.cities = {}
            
        def find_city_by_name(self, city_name):
            return self.cities.get(city_name)
            
        def validate_city_name_and_get_id(self, city_name):
            city = self.find_city_by_name(city_name)
            return city.get('id') if city else None
    
    # Create an instance of our mock service
    mock_service = MockCityService()
    
    # Add test data
    mock_service.cities = {
        "New York": {
            'id': 'city_123',
            'name': 'New York',
            'code': 'NYC'
        }
    }
    
    # Patch the CityService class to return our mock instance
    with patch('app.services.city_service.CityService') as mock_class:
        mock_class.return_value = mock_service
        yield mock_service

if __name__ == "__main__":
    pytest.main([__file__])
