"""
Version 1 of the API.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Body, HTTPException, status
from typing import Any, Dict
import logging

from app.services import intent_service, gemini_service, session_service
from app.core.logging import get_logger

from .routes_auth import router as auth_router
from .routes_user import router as user_router
from .routes_session import router as session_router

# Create the v1 router
router = APIRouter()
logger = get_logger(__name__)

# Mock user for development
async def get_mock_user():
    return {"id": 1, "email": "<EMAIL>", "is_active": True, "is_superuser": False}

@router.post("/gemini-chat-with-intent")
async def gemini_chat_with_intent(
    message: Dict[str, str],
    session_id: str = Header(..., description="Session identifier"),
) -> Dict[str, Any]:
    """
    Handle chat with Gemini model and intent detection.
    This combines both chat and intent detection in a single endpoint.
    """
    try:
        current_user = await get_mock_user()
        logger.info(f"Processing Gemini chat with intent for session: {session_id}")
        
        # First, get intent from the message
        intent_result = intent_service.detect_intent(message.get("message", ""))
        
        # Process the message with the Gemini service
        gemini_response = gemini_service.generate_response(
            message=message.get("message", ""),
            context=f"Intent: {intent_result.get('intent', 'general')}"
        )
        
        # Save the interaction in session
        session_service.add_to_session(
            session_id=session_id,
            user_id=current_user["id"],
            user_message=message.get("message", ""),
            bot_response=gemini_response,
            intent=intent_result.get('intent', 'general')
        )
        
        return {
            "response": gemini_response,
            "session_id": session_id,
            "intent": intent_result.get('intent', 'general')
        }
        
    except Exception as e:
        logger.error(f"Error in gemini-chat-with-intent: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing your message with Gemini"
        )

# Include all version 1 routers
router.include_router(auth_router, tags=["authentication"])
router.include_router(user_router, prefix="/users", tags=["users"])
router.include_router(session_router, prefix="/sessions", tags=["sessions"])
