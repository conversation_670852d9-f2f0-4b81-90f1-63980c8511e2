"""
Version 1 of the API.
"""
from fastapi import APIRouter

from .routes_auth import router as auth_router
from .routes_user import router as user_router
from .routes_session import router as session_router

# Create the v1 router
router = APIRouter()

# Include all version 1 routers
router.include_router(auth_router, tags=["authentication"])
router.include_router(user_router, prefix="/users", tags=["users"])
router.include_router(session_router, prefix="/sessions", tags=["sessions"])
