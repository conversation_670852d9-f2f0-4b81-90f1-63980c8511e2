"""
Session models for the onboarding API.

This module defines models for managing user session state during flows.
"""

from enum import Enum
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime


class FlowType(str, Enum):
    """Types of user flows."""
    
    SIGNUP = "signup"
    LOGIN = "login"
    CHAT = "chat"
    UNKNOWN = "unknown"


class FlowStep(str, Enum):
    """Steps in user flows."""
    
    # Common steps
    INITIAL = "initial"
    COMPLETED = "completed"
    ERROR = "error"
    
    # Signup flow steps
    NAME = "name"
    EMAIL = "email"
    PASSWORD = "password"
    CITY = "city"
    CONFIRMATION = "confirmation"
    
    # Login flow steps
    LOGIN_EMAIL = "login_email"
    LOGIN_PASSWORD = "login_password"
    LOGIN_VERIFICATION = "login_verification"
    
    # Chat flow steps
    CHAT_ACTIVE = "chat_active"
    CHAT_WAITING = "chat_waiting"


class UserData(BaseModel):
    """User data collected during flows."""
    
    name: Optional[str] = Field(
        default=None,
        description="User's full name"
    )
    email: Optional[str] = Field(
        default=None,
        description="User's email address"
    )
    password: Optional[str] = Field(
        default=None,
        description="User's password (hashed)"
    )
    city: Optional[str] = Field(
        default=None,
        description="User's city"
    )
    
    class Config:
        """Pydantic configuration."""
        # Don't include password in serialization by default
        fields = {
            'password': {'write_only': True}
        }


class SessionData(BaseModel):
    """Session data for tracking user state."""
    
    flow_type: FlowType = Field(
        default=FlowType.UNKNOWN,
        description="Type of flow the user is in"
    )
    flow_step: FlowStep = Field(
        default=FlowStep.INITIAL,
        description="Current step in the flow"
    )
    collected_data: Dict[str, Any] = Field(
        default_factory=dict,
        description="Data collected during the flow"
    )
    user_data: Optional[UserData] = Field(
        default=None,
        description="Structured user data"
    )
    created_at: str = Field(
        ...,
        description="ISO timestamp when session was created"
    )
    last_activity: str = Field(
        ...,
        description="ISO timestamp of last activity"
    )
    expires_at: Optional[str] = Field(
        default=None,
        description="ISO timestamp when session expires"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional session metadata"
    )
    
    def is_expired(self) -> bool:
        """Check if the session has expired."""
        if not self.expires_at:
            return False
        
        try:
            expires = datetime.fromisoformat(self.expires_at.replace('Z', '+00:00'))
            return datetime.now(expires.tzinfo) > expires
        except (ValueError, AttributeError):
            return False
    
    def update_activity(self) -> None:
        """Update the last activity timestamp."""
        from datetime import datetime, timezone
        self.last_activity = datetime.now(timezone.utc).isoformat()
    
    def set_flow(self, flow_type: FlowType, flow_step: FlowStep = FlowStep.INITIAL) -> None:
        """Set the flow type and step."""
        self.flow_type = flow_type
        self.flow_step = flow_step
        self.update_activity()
    
    def advance_step(self, next_step: FlowStep) -> None:
        """Advance to the next step in the flow."""
        self.flow_step = next_step
        self.update_activity()
    
    def add_data(self, key: str, value: Any) -> None:
        """Add data to the collected data."""
        self.collected_data[key] = value
        self.update_activity()
    
    def get_data(self, key: str, default: Any = None) -> Any:
        """Get data from the collected data."""
        return self.collected_data.get(key, default)
