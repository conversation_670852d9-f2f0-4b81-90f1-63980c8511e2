#!/usr/bin/env python3
"""
Simple test script to verify imports work correctly.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly."""
    # Test pydantic-settings import
    print("Testing pydantic-settings import...")
    from pydantic_settings import BaseSettings
    print("✓ pydantic-settings imported successfully")
    assert BaseSettings is not None, "Failed to import BaseSettings"

    # Test core config import
    print("Testing core config import...")
    from app.core.config import Settings
    print("✓ Settings class imported successfully")
    assert Settings is not None, "Failed to import Settings"

    # Test creating settings without requiring environment variables
    print("Testing settings creation...")
    try:
        from app.core.config import settings
        print(f"✓ Config loaded successfully. App name: {settings.app_name}")
        assert hasattr(settings, 'app_name'), "Settings missing app_name"
    except Exception as config_error:
        print(f"⚠️ Config loading failed (expected if .env missing): {config_error}")
        print("This is normal if HF_TOKEN and GEMINI_API_KEY are not set")

    # Test model imports
    print("Testing model imports...")
    from app.models.requests import MessageRequest, LoginRequest, SignupRequest
    print("✓ Request models imported successfully")
    assert all([MessageRequest, LoginRequest, SignupRequest]), "Failed to import request models"

    # Test model creation
    print("Testing basic model creation...")
    msg = MessageRequest(message="test message")
    print(f"✓ MessageRequest created: {msg.message}")
    assert msg.message == "test message", "MessageRequest creation failed"

    print("\n🎉 Core imports successful! The Pydantic v2 compatibility fixes work correctly.")

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
