["tests/test_api.py::TestAPIFlow::test_complete_signup_flow", "tests/test_api.py::TestGeminiModelsEndpoint::test_list_gemini_models_success", "tests/test_api.py::TestHealthEndpoint::test_health_check", "tests/test_api.py::TestOnboardingEndpoint::test_onboarding_intent_empty_message", "tests/test_api.py::TestOnboardingEndpoint::test_onboarding_intent_invalid_request", "tests/test_api.py::TestOnboardingEndpoint::test_onboarding_intent_success", "tests/test_api_endpoints.py::test_debug_intent", "tests/test_api_endpoints.py::test_gemini_chat_with_intent", "tests/test_api_endpoints.py::test_health", "tests/test_api_endpoints.py::test_onboarding", "tests/test_api_routes.py::TestCombinedChatEndpoint::test_combined_chat_login_flow", "tests/test_api_routes.py::TestCombinedChatEndpoint::test_combined_chat_missing_session_id", "tests/test_api_routes.py::TestCombinedChatEndpoint::test_combined_chat_signup_flow", "tests/test_api_routes.py::TestCombinedChatEndpoint::test_combined_chat_success", "tests/test_api_routes.py::TestGeminiChatEndpoint::test_gemini_chat_empty_message", "tests/test_api_routes.py::TestGeminiChatEndpoint::test_gemini_chat_success", "tests/test_api_routes.py::TestGeminiModelsEndpoint::test_list_gemini_models_success", "tests/test_api_routes.py::TestHealthEndpoint::test_health_check", "tests/test_api_routes.py::TestOnboardingEndpoint::test_onboarding_intent_empty_message", "tests/test_api_routes.py::TestOnboardingEndpoint::test_onboarding_intent_invalid_request", "tests/test_api_routes.py::TestOnboardingEndpoint::test_onboarding_intent_success", "tests/test_auth_flows.py::TestAuthenticationServiceSignup::test_signup_api_failure", "tests/test_auth_flows.py::TestAuthenticationServiceSignup::test_successful_signup", "tests/test_auth_flows.py::TestLoginFlow::test_invalid_credentials", "tests/test_auth_flows.py::TestLoginFlow::test_successful_login", "tests/test_auth_flows.py::TestSignupRequest::test_full_signup_request", "tests/test_auth_flows.py::TestSignupRequest::test_minimal_signup_request", "tests/test_deployment.py::test_api_health", "tests/test_deployment.py::test_api_intent_classification", "tests/test_deployment.py::test_mcp_server", "tests/test_email_validation.py::test_invalid_email_login", "tests/test_email_validation.py::test_valid_email_login", "tests/test_import.py::test_imports", "tests/test_intent_service.py::test_intent_service", "tests/test_intent_service.py::test_raw_pipeline", "tests/test_login_flow.py::test_health_check", "tests/test_login_flow.py::test_login_flow", "tests/test_mcp.py::test_mcp_chat_with_intent", "tests/test_mcp.py::test_mcp_classify_intent", "tests/test_models.py::TestLoginRequest::test_email_validation", "tests/test_models.py::TestLoginRequest::test_password_validation", "tests/test_models.py::TestLoginRequest::test_valid_login_request", "tests/test_models.py::TestMessageRequest::test_empty_message_validation", "tests/test_models.py::TestMessageRequest::test_message_length_validation", "tests/test_models.py::TestMessageRequest::test_message_sanitization", "tests/test_models.py::TestMessageRequest::test_valid_message_request", "tests/test_models.py::TestResponseModels::test_combined_chat_response", "tests/test_models.py::TestResponseModels::test_gemini_chat_response", "tests/test_models.py::TestResponseModels::test_intent_classification_result", "tests/test_models.py::TestResponseModels::test_onboarding_response", "tests/test_models.py::TestSessionModels::test_flow_enums", "tests/test_models.py::TestSessionModels::test_session_data", "tests/test_models.py::TestSessionModels::test_session_data_defaults", "tests/test_models.py::TestSessionModels::test_user_data", "tests/test_models.py::TestSessionModels::test_user_data_to_dict", "tests/test_models.py::TestSignupRequest::test_name_sanitization", "tests/test_models.py::TestSignupRequest::test_name_validation", "tests/test_models.py::TestSignupRequest::test_valid_signup_request", "tests/test_realtime_validation.py::test_realtime_email_validation", "tests/test_realtime_validation.py::test_signup_validation", "tests/test_security.py::TestEmailValidation::test_validate_email_invalid", "tests/test_security.py::TestEmailValidation::test_validate_email_valid", "tests/test_security.py::TestInjectionDetection::test_detect_safe_content", "tests/test_security.py::TestInjectionDetection::test_detect_sql_injection", "tests/test_security.py::TestInjectionDetection::test_detect_xss_injection", "tests/test_security.py::TestInputSanitization::test_sanitize_input_basic", "tests/test_security.py::TestInputSanitization::test_sanitize_input_empty", "tests/test_security.py::TestInputSanitization::test_sanitize_input_html_escape", "tests/test_security.py::TestInputSanitization::test_sanitize_input_length_limit", "tests/test_security.py::TestRateLimiting::test_rate_limit_headers", "tests/test_security.py::TestRateLimiting::test_rate_limit_within_bounds", "tests/test_security.py::TestSafeStringValidation::test_is_safe_string_basic", "tests/test_security.py::TestSafeStringValidation::test_is_safe_string_dangerous_chars", "tests/test_security.py::TestSafeStringValidation::test_is_safe_string_with_special_chars", "tests/test_security.py::TestSecurityHeaders::test_security_headers_present", "tests/test_security.py::TestSecurityHeaders::test_server_header_removed", "tests/test_security.py::TestSensitiveDataMasking::test_mask_sensitive_data_basic", "tests/test_security.py::TestSensitiveDataMasking::test_mask_sensitive_data_empty", "tests/test_security.py::TestSensitiveDataMasking::test_mask_sensitive_data_short", "tests/test_security.py::TestURLValidation::test_validate_url_custom_schemes", "tests/test_security.py::TestURLValidation::test_validate_url_invalid", "tests/test_security.py::TestURLValidation::test_validate_url_valid", "tests/test_service_directly.py::test_service", "tests/test_services.py::TestCityService::test_find_city_by_name_not_found", "tests/test_services.py::TestCityService::test_find_city_by_name_success", "tests/test_services.py::TestIntentService::test_classify_intent_low_confidence", "tests/test_services.py::TestIntentService::test_classify_intent_success", "tests/test_signup_api.py::TestAuthenticationServiceSignup::test_signup_api_failure", "tests/test_signup_api.py::TestAuthenticationServiceSignup::test_signup_timeout", "tests/test_signup_api.py::TestAuthenticationServiceSignup::test_successful_signup", "tests/test_signup_api.py::TestSignupRequest::test_full_signup_request", "tests/test_signup_api.py::TestSignupRequest::test_invalid_birthday_validation", "tests/test_signup_api.py::TestSignupRequest::test_invalid_email_validation", "tests/test_signup_api.py::TestSignupRequest::test_invalid_phone_validation", "tests/test_signup_api.py::TestSignupRequest::test_minimal_signup_request", "tests/test_validation.py::TestCityNameValidation::test_city_name_validation_not_found", "tests/test_validation.py::TestCityNameValidation::test_city_name_validation_success", "tests/test_validation.py::TestEmailValidation::test_invalid_email_format", "tests/test_validation.py::TestEmailValidation::test_valid_email_format", "tests/test_validation.py::TestRealtimeValidation::test_realtime_city_validation", "tests/test_validation.py::TestRealtimeValidation::test_realtime_email_validation"]