"""
Session management routes.
"""
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, status, Header, Body
from pydantic import BaseModel

from app.core.logging import get_logger
from app.services.session_service import session_service
from app.services.intent_service import intent_service
from app.services.gemini_service import gemini_service

router = APIRouter()
logger = get_logger(__name__)

# Mock user for development
async def get_mock_user():
    return {"id": 1, "email": "<EMAIL>", "is_active": True, "is_superuser": False}

# Response models
class MessageRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    session_id: str

class Session(BaseModel):
    id: str
    user_id: int
    created_at: str
    updated_at: str

class Msg(BaseModel):
    msg: str

# Mock user for development
async def get_mock_user():
    return {"id": 1, "email": "<EMAIL>", "is_active": True, "is_superuser": False}

@router.post("/chat", response_model=ChatResponse)
async def chat_with_session(
    message: MessageRequest,
    session_id: str = Header(..., description="Session identifier"),
) -> Any:
    """
    Handle chat with session-based context.
    """
    try:
        current_user = await get_mock_user()
        logger.info(f"Processing chat for session: {session_id}")
        
        # Process the message with the session service
        response = session_service.process_message(
            session_id=session_id,
            user_id=current_user["id"],
            message=message.message
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error processing chat message: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing your message"
        )

@router.get("/{session_id}", response_model=Session)
async def get_session(session_id: str) -> Any:
    """
    Get session by ID.
    """
    try:
        current_user = await get_mock_user()
        session = session_service.get_session(session_id, current_user["id"])
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        return session
    except Exception as e:
        logger.error(f"Error getting session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving the session"
        )

@router.get("/", response_model=List[Session])
async def list_sessions(
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Retrieve user's sessions.
    """
    try:
        current_user = await get_mock_user()
        sessions = session_service.get_user_sessions(
            user_id=current_user["id"],
            skip=skip,
            limit=limit
        )
        return sessions
    except Exception as e:
        logger.error(f"Error listing sessions: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving sessions"
        )

@router.delete("/{session_id}", response_model=Msg)
async def delete_session(session_id: str) -> Any:
    """
    Delete a session.
    """
    try:
        current_user = await get_mock_user()
        session_service.delete_session(session_id, current_user["id"])
        return {"msg": "Session deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while deleting the session"
        )

@router.post("/{session_id}/reset", response_model=Msg)
async def reset_session(session_id: str) -> Any:
    """
    Reset a session, clearing its history.
    """
    try:
        current_user = await get_mock_user()
        session_service.reset_session(session_id, current_user["id"])
        return {"msg": "Session reset successfully"}
    except Exception as e:
        logger.error(f"Error resetting session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while resetting the session"
        )
