"""
MCP (Model Context Protocol) Server for Onboarding Assistant.

This server provides MCP tools for intent classification and chat functionality,
connecting to the main FastAPI server.
"""

import os
import sys
from pathlib import Path
from fastmcp import FastMCP
import requests
from typing import Dict, Any, Optional

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Try to import settings, fallback to environment variables if not available
try:
    from app.core.config import settings
    API_BASE_URL = settings.api_base_url
except ImportError:
    # Fallback to environment variables for deployment
    API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")

# Initialize MCP server
mcp = FastMCP("onboarding-assistant")

@mcp.tool()
def classify_intent(message: str) -> Dict[str, Any]:
    """
    Classify the intent of a user message.

    Args:
        message: The user message to classify

    Returns:
        Dictionary containing intent classification results
    """
    try:
        response = requests.post(
            f"{API_BASE_URL}/onboarding",
            json={"message": message},
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {
            "error": f"Failed to classify intent: {str(e)}",
            "message": message,
            "status": "error"
        }

@mcp.tool()
def chat_with_intent(message: str, session_id: str) -> Dict[str, Any]:
    """
    Chat with Gemini AI using intent-aware processing.

    Args:
        message: The user message
        session_id: Session identifier for conversation context

    Returns:
        Dictionary containing chat response
    """
    try:
        response = requests.post(
            f"{API_BASE_URL}/gemini-chat-with-intent",
            json={"message": message},
            headers={"session_id": session_id},
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        return {
            "error": f"Failed to process chat: {str(e)}",
            "message": message,
            "session_id": session_id,
            "status": "error"
        }

def run_mcp_server():
    """Run the MCP server over STDIO for Claude Desktop."""
    print("🚀 Starting MCP server via STDIO...", file=sys.stderr)
    print(f"🔗 Connected to API Base URL: {API_BASE_URL}", file=sys.stderr)
    mcp.run()  # No transport/host/port — STDIO communication only

if __name__ == "__main__":
    run_mcp_server()
