'use client';

import { User, Bot, Home } from 'lucide-react';
import { useEffect, useState } from 'react';

type Message = {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isLoading?: boolean;
};

const LoadingDots = () => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return '.'.repeat(prev.length + 1);
      });
    }, 300);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center space-x-1 h-6">
      <div className="flex space-x-1">
        <div className="w-2 h-2 rounded-full bg-muted-foreground/80 animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-2 h-2 rounded-full bg-muted-foreground/80 animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-2 h-2 rounded-full bg-muted-foreground/80 animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
      <span className="w-4 inline-block">{dots}</span>
    </div>
  );
};

export default function ChatMessage({ message }: { message: Message }) {
  const isAI = message.sender === 'ai';

  return (
    <div className={`w-full transition-colors duration-200 ${
      isAI ? 'bg-muted/10' : 'bg-transparent hover:bg-muted/5'
    }`}>
      <div className={`max-w-4xl mx-auto px-4 py-4 ${!isAI ? 'flex justify-end' : ''}`}>
        <div className={`flex gap-3 items-start ${!isAI ? 'flex-row-reverse' : ''} max-w-[90%] sm:max-w-[85%] md:max-w-[80%]`}>
          {/* Avatar */}
          <div className={`flex-shrink-0 w-9 h-9 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 ${
            isAI
              ? 'bg-gradient-to-br from-blue-500 to-violet-600 text-white shadow-lg shadow-blue-500/20'
              : 'bg-gradient-to-br from-green-500 to-blue-600 text-white shadow-lg shadow-blue-500/20 text-white border-2 border-primary/20'
          }`}>
            {isAI ? (
              <Home className="w-5 h-5" />
            ) : (
              <User className="w-5 h-5" />
            )}
          </div>

          {/* Message Content */}
          <div className={`flex-1 min-w-0 ${!isAI ? 'text-right' : ''}`}>
            {/* Header */}
            <div className={`flex items-center gap-2 mb-1.5 ${!isAI ? 'justify-end' : ''}`}>
              <span className="font-medium text-sm text-foreground">
                {isAI ? 'My Village AI' : 'You'}
              </span>
              <span className="text-xs text-muted-foreground">
                {message.timestamp.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true
                })}
              </span>
            </div>

            {/* Message Bubble */}
            <div className={`inline-block rounded-2xl px-4 py-2.5 transition-all duration-200 ${
              isAI
                ? 'bg-card border border-border/30 text-card-foreground shadow-sm rounded-tl-none'
                : 'bg-primary text-primary-foreground shadow-md rounded-tr-none'
            }`}>
              <div className="text-sm leading-relaxed whitespace-pre-wrap">
                {message.isLoading ? <LoadingDots /> : message.content}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
