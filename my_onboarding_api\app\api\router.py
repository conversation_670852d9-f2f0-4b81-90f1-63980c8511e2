"""
Main API router that combines all API version routers.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException, status
from typing import Any, Dict
from pydantic import BaseModel

from app.services import intent_service, gemini_service, session_service
from app.core.logging import get_logger

# Import versioned routers
from .v1 import router as v1_router

# Create main router
api_router = APIRouter()
logger = get_logger(__name__)

# Mock user for development
async def get_mock_user():
    return {"id": 1, "email": "<EMAIL>", "is_active": True, "is_superuser": False}

# Request/Response models for backward compatibility
class MessageRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    session_id: str
    intent: str = None

# Backward compatibility endpoint - keep the original path
@api_router.post("/gemini-chat-with-intent", response_model=ChatResponse)
async def gemini_chat_with_intent_compat(
    message: MessageRequest,
    session_id: str = Header(..., description="Session identifier"),
) -> Any:
    """
    Handle chat with Gemini model and intent detection.
    This endpoint maintains backward compatibility with the original API.
    """
    try:
        current_user = await get_mock_user()
        logger.info(f"Processing Gemini chat with intent for session: {session_id}")

        # First, get intent from the message
        intent_result = intent_service.detect_intent(message.message)

        # Process the message with the Gemini service
        gemini_response = gemini_service.generate_response(
            message=message.message,
            context=f"Intent: {intent_result.get('intent', 'general')}"
        )

        # Save the interaction in session
        session_service.add_to_session(
            session_id=session_id,
            user_id=current_user["id"],
            user_message=message.message,
            bot_response=gemini_response,
            intent=intent_result.get('intent', 'general')
        )

        return {
            "response": gemini_response,
            "session_id": session_id,
            "intent": intent_result.get('intent', 'general')
        }

    except Exception as e:
        logger.error(f"Error in gemini-chat-with-intent: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing your message with Gemini"
        )

# Include versioned routers
api_router.include_router(v1_router, prefix="/api/v1")
