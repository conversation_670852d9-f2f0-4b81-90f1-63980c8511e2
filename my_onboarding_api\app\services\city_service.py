"""
City Service for finding cities by exact name in AWS DynamoDB.
"""
import os
import boto3
from typing import Dict, List, Optional
from botocore.exceptions import ClientError
import logging

# Set up logging
logger = logging.getLogger(__name__)

class CityService:
    """Service to handle city lookup operations with DynamoDB."""
    
    def __init__(self):
        """Initialize the DynamoDB resource and table."""
        self.table_name = os.getenv('DYNAMODB_CITIES_TABLE', 'cities')
        self.region = os.getenv('AWS_REGION', 'us-east-1')

        logger.info(f"Initializing DynamoDB service with table: {self.table_name} in region: {self.region}")
        
        # Safely log AWS credentials if they exist
        aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret = os.getenv('AWS_SECRET_ACCESS_KEY')
        aws_token = os.getenv('AWS_SESSION_TOKEN')
        
        logger.info(f"  AWS_ACCESS_KEY_ID: {aws_access_key[:5] + '...' if aws_access_key else 'Not set'}")
        logger.info(f"  AWS_SECRET_ACCESS_KEY: {'*****' if aws_secret else 'Not set'}")
        logger.info(f"  AWS_SESSION_TOKEN: {'Set' if aws_token else 'Not set'}")
        
        # Initialize DynamoDB client
        self.dynamodb = boto3.resource(
            'dynamodb',
            region_name=self.region,
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            aws_session_token=os.getenv('AWS_SESSION_TOKEN')
        )
        
        self.table = self.dynamodb.Table(self.table_name)
    
    def find_city_by_name(self, city_name: str) -> Optional[Dict]:
        """
        Find a city by name (case-insensitive) and filter out deleted items.
        
        Args:
            city_name (str): The name of the city to find.
            
        Returns:
            dict: The city data if found, None otherwise.
        """
        if not city_name:
            return None
            
        logger.info(f"Searching for city with name: {city_name}")
        search_name = city_name.strip().lower()
        
        try:
            # First, try to find an exact match using a scan with filter
            # We'll fetch all items and filter in memory for case-insensitive search
            scan_params = {
                'FilterExpression': '#isDeleted <> :true',
                'ExpressionAttributeNames': {
                    '#isDeleted': 'isDeleted'
                },
                'ExpressionAttributeValues': {
                    ':true': 'true'  # Filter out deleted cities
                },
                'Limit': 100  # Process 100 items at a time
            }
            
            while True:
                response = self.table.scan(**scan_params)
                
                # Process items from this batch
                for item in response.get('Items', []):
                    city = self._convert_dynamo_item(item)
                    
                    # Double check isDeleted in case the filter didn't work
                    if str(city.get('isDeleted', 'false')).lower() == 'true':
                        continue
                        
                    # Check for exact match (case-insensitive)
                    if city.get('name', '').lower() == search_name:
                        logger.info(f"Found exact match: {city}")
                        return city
                
                # If we didn't find a match, check if there are more items to scan
                if 'LastEvaluatedKey' not in response:
                    break
                    
                # Set the start key for the next scan
                scan_params['ExclusiveStartKey'] = response['LastEvaluatedKey']
            
            # If we get here, no exact match was found
            logger.info(f"No exact match found for city: {city_name}")
            return None
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code')
            error_msg = e.response.get('Error', {}).get('Message', str(e))
            logger.error(f"DynamoDB error finding city '{city_name}': {error_code} - {error_msg}")
            logger.debug(f"Full error details: {e}")
            return None

    def validate_city_name_and_get_id(self, city_name: str) -> Optional[str]:
        """
        Validate a city name and return its ID if found.

        Args:
            city_name (str): The name of the city to validate.

        Returns:
            str: The city ID if found, None otherwise.
        """
        if not city_name or not city_name.strip():
            return None

        city_data = self.find_city_by_name(city_name.strip())
        if city_data:
            city_id = city_data.get('id') or city_data.get('cityId')
            logger.info(f"Successfully validated city '{city_name}' -> ID: {city_id}")
            return city_id
        else:
            logger.warning(f"City name '{city_name}' not found in database")
            return None

    def validate_city_names_and_get_ids(self, city_names: List[str]) -> Dict[str, Optional[str]]:
        """
        Validate multiple city names and return their IDs.

        Args:
            city_names (List[str]): List of city names to validate.

        Returns:
            Dict[str, Optional[str]]: Dictionary mapping city names to their IDs.
                                    Invalid city names will have None as their value.
        """
        result = {}
        for city_name in city_names:
            if city_name and city_name.strip():
                city_id = self.validate_city_name_and_get_id(city_name.strip())
                result[city_name.strip()] = city_id
            else:
                result[city_name] = None

        return result
            
    def _convert_dynamo_item(self, item):
        """Convert DynamoDB item to a more Pythonic format."""
        result = {}
        for key, value in item.items():
            if isinstance(value, dict):
                # Handle DynamoDB JSON format (old format)
                if 'S' in value:
                    result[key] = value['S']
                elif 'N' in value:
                    result[key] = float(value['N']) if '.' in value['N'] else int(value['N'])
                elif 'BOOL' in value:
                    result[key] = value['BOOL']
            else:
                # Handle direct value format (new format)
                result[key] = value
        return result

# Singleton instance
_city_service_instance = None

def get_city_service():
    """Get the singleton instance of CityService."""
    global _city_service_instance
    if _city_service_instance is None:
        _city_service_instance = CityService()
    return _city_service_instance

# For backward compatibility
city_service = get_city_service()
