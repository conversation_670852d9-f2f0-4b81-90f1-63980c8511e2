"use client";

import { useState, useRef, useEffect } from "react";
import dynamic from "next/dynamic";

// Dynamically import components to avoid SSR issues
const ChatInput = dynamic(() => import("@/components/ChatInput"), {
  ssr: false,
});
const ChatMessage = dynamic(() => import("@/components/ChatMessage"), {
  ssr: false,
});

type Message = {
  id: string;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
  isLoading?: boolean;
};

// Error Boundary Component
function ErrorBoundary({ children }: { children: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const errorHandler = (event: ErrorEvent) => {
      console.error('Global error caught:', event.error);
      setHasError(true);
      return false;
    };

    window.addEventListener('error', errorHandler);
    return () => window.removeEventListener('error', errorHandler);
  }, []);

  if (hasError) {
    return (
      <div className="p-4 text-red-600">
        <h2 className="text-xl font-bold mb-2">Something went wrong</h2>
        <p>Please refresh the page and try again.</p>
        <button 
          onClick={() => window.location.reload()}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh Page
        </button>
      </div>
    );
  }

  return <>{children}</>;
}

export default function Home() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content:
        "Hello! I'm My Village AI, your community assistant. How can I help you today?",
      sender: "ai",
      timestamp: new Date(),
    },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const handleSendMessage = async (content: string) => {
    const messageId = Date.now().toString();
    
    // Create user message
    const newUserMessage: Message = {
      id: `user-${messageId}`,
      content,
      sender: "user",
      timestamp: new Date(),
    };

    // Add AI loading message
    const loadingMessage: Message = {
      id: `loading-${messageId}`,
      content: '',
      sender: 'ai',
      timestamp: new Date(),
      isLoading: true
    };

    setMessages((prev) => [...prev, newUserMessage, loadingMessage]);
    setIsLoading(true);

    try {
      // Make API call
      const response = await fetch(
        "http://localhost:8000/gemini-chat-with-intent",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            session_id: "12345", // TODO: Implement proper session management
          },
          body: JSON.stringify({ message: content }),
        }
      );

      const data = await response.json();

      // Handle API errors
      if (!response.ok) {
        const errorMessage =
          data.detail?.message ||
          data.message ||
          "Failed to process your request";
        throw new Error(errorMessage);
      }

      // Process different response formats based on flow type
      let reply: string;

      // Handle different response structures
      if (data.status) {
        // Direct response with status (from our updated backend)
        if (data.status === "success") {
          reply = data.message || "Operation completed successfully!";
        } else {
          // Try to extract detailed error message
          const errorDetails =
            data.data?.validation_error || data.data?.error || data.message;
          reply = errorDetails || "There was an issue with your request.";
        }
      } else if (data.flow_type) {
        // Handle flow-based responses
        if (data.result) {
          // Nested result object
          reply =
            data.result.message ||
            data.message ||
            "Please provide the required information.";
        } else {
          // Direct message
          reply = data.message || "Please provide the required information.";
        }
      } else {
        // Handle regular chat responses
        reply =
          data.gemini_response ||
          data.response ||
          data.message ||
          "I'm sorry, I couldn't process your request.";
      }

      // Create and add AI response
      // Update the loading message with the actual response
      setMessages(prev => {
        const newMessages = [...prev];
        // Find the most recent loading message
        const loadingMessages = newMessages.filter(msg => msg.id.startsWith('loading-'));
        const lastLoadingMessage = loadingMessages[loadingMessages.length - 1];
        
        if (lastLoadingMessage) {
          const loadingIndex = newMessages.findIndex(msg => msg.id === lastLoadingMessage.id);
          if (loadingIndex !== -1) {
            newMessages[loadingIndex] = {
              ...newMessages[loadingIndex],
              id: `ai-${Date.now()}`, // Give it a new ID to avoid any potential issues
              content: reply,
              isLoading: false,
              timestamp: new Date()
            };
          }
        } else {
          // Fallback in case loading message is not found
          const aiResponse: Message = {
            id: `ai-${Date.now()}`,
            content: reply,
            sender: "ai",
            timestamp: new Date(),
            isLoading: false
          };
          newMessages.push(aiResponse);
        }
        
        return newMessages;
      });
    } catch (error: unknown) {
      // Enhanced error logging
      if (error instanceof Error) {
        console.error("API Error:", {
          name: error.name,
          message: error.message,
          stack: error.stack,
        });
      } else if (typeof error === 'object' && error !== null) {
        try {
          // Handle error objects that might not be Error instances
          console.error("API Error:", {
            ...error,
            // Safely stringify any nested objects
            response: error.response ? JSON.parse(JSON.stringify(error.response)) : undefined,
          });
        } catch (e) {
          // Fallback for non-serializable errors
          console.error("API Error (could not stringify):", String(error));
        }
      } else {
        console.error("API Error:", String(error));
      }

      // Default error message
      let errorMessage = "Sorry, I'm having trouble processing your request. Please try again later.";

      // Handle different error types with better type safety
      if (error instanceof Error) {
        try {
          // Try to parse error message as JSON
          const errorData = JSON.parse(error.message);
          errorMessage =
            errorData.message ||
            errorData.detail?.message ||
            errorData.error ||
            error.message;
        } catch (e) {
          // If not JSON, use the error message as is
          errorMessage = error.message || 'An unknown error occurred';
        }
      } else if (typeof error === 'object' && error !== null) {
        // Handle object errors with type safety
        const errorObj = error as Record<string, any>;
        if (errorObj.response?.data) {
          const data = errorObj.response.data;
          errorMessage = String(
            data.message || data.error || "An error occurred with your request"
          );
        } else if (errorObj.message) {
          errorMessage = String(errorObj.message);
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Clean up error message
      errorMessage = errorMessage
        .replace(/\[object Object\]/g, "Error")
        .replace(/Error: /g, "");

      // Add more context for authentication errors
      if (
        errorMessage.toLowerCase().includes("auth") ||
        errorMessage.toLowerCase().includes("login") ||
        errorMessage.toLowerCase().includes("401")
      ) {
        errorMessage =
          "Login failed. Please check your email and password and try again.";
      }

      // Update the loading message with error
      setMessages(prev => {
        const newMessages = [...prev];
        // Find the most recent loading message
        const loadingMessages = newMessages.filter(msg => msg.id?.startsWith('loading-'));
        const lastLoadingMessage = loadingMessages[loadingMessages.length - 1];
        
        if (lastLoadingMessage) {
          const loadingIndex = newMessages.findIndex(msg => msg.id === lastLoadingMessage.id);
          if (loadingIndex !== -1) {
            newMessages[loadingIndex] = {
              ...newMessages[loadingIndex],
              id: `error-${Date.now()}`,
              content: `❌ ${errorMessage}`,
              isLoading: false,
              timestamp: new Date()
            };
          }
        } else {
          // Fallback in case loading message is not found
          const errorResponse: Message = {
            id: `error-${Date.now()}`,
            content: `❌ ${errorMessage}`,
            sender: "ai",
            timestamp: new Date(),
            isLoading: false
          };
          newMessages.push(errorResponse);
        }
        
        return newMessages;
      });

      // Enhanced error logging with more context
      console.error("Error details:", {
        error: error instanceof Error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : String(error),
        timestamp: new Date().toISOString(),
        userMessage: content,
        environment: process.env.NODE_ENV,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  return (
    <ErrorBoundary>
      <div className="flex flex-col h-screen bg-gradient-to-br from-muted/30 to-background">
      <header className="border-b border-border/50 bg-card/80 backdrop-blur-sm shadow-soft">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-village-gradient flex items-center justify-center shadow-village-glow">
              <svg
                className="w-5 h-5 text-white"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
              </svg>
            </div>
            <div>
              <h1 className="text-xl font-semibold text-foreground">
                My Village AI
              </h1>
              <p className="text-xs text-muted-foreground">
                Your Community Assistant
              </p>
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1 overflow-y-auto">
        <div className="pb-32">
          {messages.map((message, index) => (
            <div
              key={message.id}
              className="animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <ChatMessage message={message} />
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </main>

      <div className="fixed bottom-0 left-0 right-0 bg-card/95 backdrop-blur-sm border-t border-border/50 py-4 shadow-large">
        <ChatInput onSendMessage={handleSendMessage} isLoading={isLoading} />
        <p className="text-xs text-center text-muted-foreground mt-3 px-4">
          My Village AI may display inaccurate info, including about people, so
          double-check its responses.
        </p>
      </div>
      </div>
    </ErrorBoundary>
  );
}
