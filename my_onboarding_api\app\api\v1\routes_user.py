"""
User routes.
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, EmailStr

router = APIRouter()

class UserBase(BaseModel):
    email: EmailStr
    is_active: bool = True
    is_superuser: bool = False

class UserCreate(UserBase):
    password: str

class UserUpdate(UserBase):
    password: Optional[str] = None

class User(UserBase):
    id: int
    
    class Config:
        from_attributes = True

# Mock data for demonstration
MOCK_USERS = [
    {"id": 1, "email": "<EMAIL>", "is_active": True, "is_superuser": True},
    {"id": 2, "email": "<EMAIL>", "is_active": True, "is_superuser": False},
]

@router.get("/", response_model=List[User])
async def read_users():
    """
    Retrieve users.
    This is a mock implementation for demonstration purposes.
    """
    return MOCK_USERS

@router.get("/me", response_model=User)
async def read_user_me():
    """
    Get current user.
    This is a mock implementation for demonstration purposes.
    """
    return MOCK_USERS[1]  # Return the regular user by default

@router.get("/{user_id}", response_model=User)
async def read_user_by_id(user_id: int):
    """
    Get a specific user by id.
    This is a mock implementation for demonstration purposes.
    """
    user = next((user for user in MOCK_USERS if user["id"] == user_id), None)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return user

@router.put("/me", response_model=User)
async def update_user_me(
    user_in: User,
):
    """
    Update own user.
    This is a mock implementation for demonstration purposes.
    """
    # In a real app, you would update the user in the database here
    # For now, just return the input data as if it was updated
    return user_in

@router.put("/{user_id}", response_model=User)
async def update_user(
    user_id: int,
    user_in: User,
):
    """
    Update a user.
    This is a mock implementation for demonstration purposes.
    """
    # In a real app, you would update the user in the database here
    # For now, just return the input data as if it was updated
    return user_in
