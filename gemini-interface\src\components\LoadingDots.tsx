'use client';

import { useEffect, useState } from 'react';

export default function LoadingDots() {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return '.'.repeat(prev.length + 1);
      });
    }, 300);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center space-x-1 h-6">
      <span className="text-muted-foreground">Thinking</span>
      <div className="flex space-x-1">
        <div className={`w-2 h-2 rounded-full bg-muted-foreground animate-bounce`} style={{ animationDelay: '0ms' }} />
        <div className={`w-2 h-2 rounded-full bg-muted-foreground animate-bounce`} style={{ animationDelay: '150ms' }} />
        <div className={`w-2 h-2 rounded-full bg-muted-foreground animate-bounce`} style={{ animationDelay: '300ms' }} />
      </div>
      <span className="w-4 inline-block">{dots}</span>
    </div>
  );
}
