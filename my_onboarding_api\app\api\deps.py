"""
Dependencies for the API.
"""
from typing import Any, Dict
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

def get_db() -> Dict[str, Any]:
    """
    Get a database session.
    
    Yields:
        Database session
    """
    # In a real app, you would validate the JWT token here
    # For now, just return a mock user
    return {"id": 1, "email": "<EMAIL>", "is_active": True, "is_superuser": False}

def get_current_active_user(current_user = Depends(get_current_user)):
    """
    Check if the current user is active.
    This is a mock implementation for demonstration purposes.
    """
    if not current_user.get("is_active"):
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

def get_current_active_superuser(current_user = Depends(get_current_user)):
    """
    Check if the current user is a superuser.
    This is a mock implementation for demonstration purposes.
    Args:
        current_user: The current authenticated user
        
    Returns:
        The active superuser
        
    Raises:
        HTTPException: If the user is not a superuser
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=400, detail="The user doesn't have enough privileges"
        )
    return current_user
