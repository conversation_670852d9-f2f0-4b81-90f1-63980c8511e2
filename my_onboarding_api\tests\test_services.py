"""
Service layer tests.

This module combines tests for various services:
- City Service
- Intent Service
- Other business logic services
"""

import pytest
from unittest.mock import Mock, patch
from app.services.city_service import CityService
from app.services.intent_service import IntentClassificationService as IntentService
from app.core.exceptions import ValidationError, IntentClassificationError

class TestCityService:
    """Tests for the CityService class."""
    
    def test_find_city_by_name_success(self, mock_dynamodb):
        """Test finding a city by name successfully."""
        # Set up the mock to return our test data
        mock_dynamodb.scan.return_value = {
            'Items': [
                {'id': 'city_123', 'name': 'New York', 'code': 'NYC'}
            ]
        }
        
        city_service = CityService()
        result = city_service.find_city_by_name("New York")
        
        # Verify the result
        assert result == {
            'id': 'city_123',
            'name': 'New York',
            'code': 'NYC'
        }
        # Verify the DynamoDB scan was called with the correct parameters
        mock_dynamodb.scan.assert_called_once()
    
    def test_find_city_by_name_not_found(self, mock_dynamodb):
        """Test finding a non-existent city by name."""
        # Set up the mock to return no results
        mock_dynamodb.scan.return_value = {'Items': []}
        
        city_service = CityService()
        result = city_service.find_city_by_name("Nonexistent City")
        
        assert result is None
        # Verify the DynamoDB scan was called with the correct parameters
        mock_dynamodb.scan.assert_called_once()


class TestIntentService:
    """Tests for the IntentClassificationService class."""
    
    def test_classify_intent_success(self, mock_intent_model):
        """Test successful intent classification."""
        # The pipeline is already initialized in __init__
        intent_service = IntentService()
        
        # Test classification
        results = intent_service.classify_intent("I want to sign up")
        
        # Verify results
        assert len(results) > 0
        result = results[0]
        assert hasattr(result, 'label')
        assert hasattr(result, 'score')
        # The mock data returns signup as the top intent with highest score
        assert result.label == "signup"
        assert 0.9 <= result.score <= 1.0  # Should be close to our mock score of 0.95
        
        # Test get_top_intent - should return the highest scoring intent
        top_intent = intent_service.get_top_intent("I want to sign up")
        assert top_intent == "signup"  # Should match the highest score in our mock
    
    def test_classify_intent_low_confidence(self):
        """Test intent classification with low confidence."""
        # Create a mock pipeline class that returns low confidence results
        class LowConfidencePipeline:
            def __init__(self, *args, **kwargs):
                pass
                
            def __call__(self, text):
                return [
                    {"label": "unknown", "score": 0.4},
                    {"label": "other", "score": 0.3}
                ]
        
        # Patch the pipeline to return our low confidence mock
        with patch('app.services.intent_service.pipeline', return_value=LowConfidencePipeline()):
            # Create a new instance of the service to use the patched pipeline
            intent_service = IntentService()
            
            # Test classification with low confidence
            results = intent_service.classify_intent("Some random text")
            
            # Verify the results
            assert len(results) > 0
            assert results[0].label == "unknown"  # Access as attribute, not dict
            assert 0 <= results[0].score <= 0.54


# Fixtures for the tests
@pytest.fixture
def mock_dynamodb():
    """Mock DynamoDB client fixture."""
    with patch('boto3.resource') as mock_boto3:
        # Create a mock table
        mock_table = Mock()
        mock_boto3.return_value.Table.return_value = mock_table
        
        # Set up the mock to return our test data
        mock_table.scan.return_value = {
            'Items': [
                {'id': 'city_123', 'name': 'New York', 'code': 'NYC'},
                {'id': 'city_456', 'name': 'Los Angeles', 'code': 'LAX'}
            ]
        }
        
        yield mock_table

@pytest.fixture
def mock_intent_model():
    """Mock intent model fixture."""
    class MockPipeline:
        def __init__(self, *args, **kwargs):
            pass
            
        def __call__(self, text):
            # Return a list of dictionaries with 'label' and 'score' keys
            return [
                [
                    {"label": "signup", "score": 0.95},
                    {"label": "greeting", "score": 0.05}
                ]
            ]
    
    # Patch the pipeline in the intent service module
    with patch('app.services.intent_service.pipeline', MockPipeline) as mock:
        yield mock

if __name__ == "__main__":
    pytest.main([__file__])
